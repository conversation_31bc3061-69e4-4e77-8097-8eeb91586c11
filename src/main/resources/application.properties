spring.application.name=AcumJobs
#
## Source DB2 Database Configuration
#db2.datasource.url=***************************************
#db2.datasource.username=db2admintest
#db2.datasource.password=db24test$2
#db2.datasource.driver-class-name=com.ibm.db2.jcc.DB2Driver
#
## Target MySQL Database Configuration
spring.datasource.target.jdbc-url=********************************************
spring.datasource.target.username=performances
spring.datasource.target.password=prf75swDexq23A
spring.datasource.target.driver-class-name=com.mysql.cj.jdbc.Driver

# Default JPA Configuration (for main application usage)
spring.datasource.source.jdbc-url=***************************************
spring.datasource.source.username=db2admintest
spring.datasource.source.password=db24test$2
spring.datasource.source.hikari.read-only=true
spring.datasource.source.driver-class-name=com.ibm.db2.jcc.DB2Driver
spring.jpa.database-platform=org.hibernate.dialect.DB2Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
server.port=8083

logging.level.org.hibernate.SQL=DEBUG
# Healthcheck Configuration