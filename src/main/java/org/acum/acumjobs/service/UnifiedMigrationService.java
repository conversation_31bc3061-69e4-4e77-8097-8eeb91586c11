package org.acum.acumjobs.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class UnifiedMigrationService {

    private final JdbcTemplate sourceJdbcTemplate;
    private final JdbcTemplate targetJdbcTemplate;
    private final MigrationLoggingService migrationLoggingService;

    /**
     * Execute a complete migration workflow in the optimal order
     */
    @Transactional("targetTransactionManager")
    public void executeCompleteMigration() {
        log.info("Starting complete unified migration process...");
        
        try {
            // Step 1: Core entities (independent)
            migrateClients();
            migrateVenuesAndHalls();
            migrateArtists();
            
            // Step 2: Dependent entities
            migratePerformanceSeries();
            migratePerformances();
            
            // Step 3: Additional data
            updateClientAddresses();
            migrateFiles();
            
            log.info("Complete unified migration completed successfully");
            
        } catch (Exception e) {
            log.error("Complete unified migration failed", e);
            throw new RuntimeException("Complete migration failed", e);
        }
    }

    /**
     * Migrate clients with address information embedded
     */
    private void migrateClients() {
        log.info("Migrating clients with addresses...");
        
        String sql = """
            INSERT INTO event_management.clients (
                client_id, client_name, client_type, client_phone, email, address, created_at, updated_at
            )
            SELECT DISTINCT
                c.ID,
                c.NAME,
                CASE 
                    WHEN c.TYPE = 1 THEN 'INDIVIDUAL'
                    WHEN c.TYPE = 2 THEN 'ORGANIZATION'
                    ELSE 'OTHER'
                END,
                c.PHONE,
                c.EMAIL,
                CONCAT_WS(', ', 
                    NULLIF(a.STREET, ''), 
                    NULLIF(a.HOUSENUMBER, ''),
                    NULLIF(a.CITY, ''),
                    NULLIF(a.ZIPCODE, '')
                ) as address,
                NOW(),
                NOW()
            FROM PERFORMANCES.CUSTOMER c
            LEFT JOIN PERFORMANCES.ADDRESS a ON c.ADDRESS = a.ID
            WHERE c.ID IS NOT NULL
            ON DUPLICATE KEY UPDATE
                client_name = VALUES(client_name),
                client_type = VALUES(client_type),
                client_phone = VALUES(client_phone),
                email = VALUES(email),
                address = VALUES(address),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} clients with addresses", rowsAffected);
    }

    /**
     * Migrate venues and halls in one operation
     */
    private void migrateVenuesAndHalls() {
        log.info("Migrating venues and halls...");
        
        // First migrate venues
        String venuesSql = """
            INSERT INTO event_management.venues (
                venue_name, venue_type, address, city, capacity, created_at, updated_at
            )
            SELECT DISTINCT
                l.NAME,
                'VENUE',
                l.ADDRESS,
                l.CITY,
                l.CAPACITY,
                NOW(),
                NOW()
            FROM PERFORMANCES.LOCATION l
            WHERE l.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                venue_name = VALUES(venue_name),
                address = VALUES(address),
                city = VALUES(city),
                capacity = VALUES(capacity),
                updated_at = NOW()
            """;
        
        int venuesAffected = targetJdbcTemplate.update(venuesSql);
        log.info("Migrated {} venues", venuesAffected);
        
        // Then migrate halls
        String hallsSql = """
            INSERT INTO event_management.halls (
                venue_id, hall_name, capacity, created_at, updated_at
            )
            SELECT 
                v.venue_id,
                COALESCE(l.HALL_NAME, 'Main Hall'),
                l.CAPACITY,
                NOW(),
                NOW()
            FROM PERFORMANCES.LOCATION l
            JOIN event_management.venues v ON v.venue_name = l.NAME
            WHERE l.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                hall_name = VALUES(hall_name),
                capacity = VALUES(capacity),
                updated_at = NOW()
            """;
        
        int hallsAffected = targetJdbcTemplate.update(hallsSql);
        log.info("Migrated {} halls", hallsAffected);
    }

    /**
     * Migrate artists from performers
     */
    private void migrateArtists() {
        log.info("Migrating artists from performers...");
        
        String sql = """
            INSERT INTO event_management.artists (
                artist_name, artist_region, artist_type, created_at, updated_at
            )
            SELECT DISTINCT
                p.NAME,
                CASE 
                    WHEN p.TYPE = 1 THEN 'LOCAL'
                    WHEN p.TYPE = 2 THEN 'FOREIGN'
                    ELSE 'LOCAL'
                END,
                'PERFORMER',
                NOW(),
                NOW()
            FROM PERFORMANCES.PERFORMER p
            WHERE p.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                artist_name = VALUES(artist_name),
                artist_region = VALUES(artist_region),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} artists", rowsAffected);
    }

    /**
     * Migrate performance series with artist mapping
     */
    private void migratePerformanceSeries() {
        log.info("Migrating performance series...");
        
        String sql = """
            INSERT INTO event_management.performance_series (
                client_id, series_name, artist_id, series_type, created_at, updated_at
            )
            SELECT DISTINCT
                p.CUSTOMER,
                p.NAME,
                a.artist_id,
                'CONCERT',
                NOW(),
                NOW()
            FROM PERFORMANCES.PERFORMANCE p
            LEFT JOIN PERFORMANCES.PERFORMER perf ON p.PERFORMER = perf.ID
            LEFT JOIN event_management.artists a ON a.artist_name = perf.NAME
            WHERE p.CUSTOMER IS NOT NULL AND p.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                series_name = VALUES(series_name),
                artist_id = VALUES(artist_id),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} performance series", rowsAffected);
    }

    /**
     * Migrate individual performances
     */
    private void migratePerformances() {
        log.info("Migrating individual performances...");
        
        String sql = """
            INSERT INTO event_management.performances (
                series_id, venue_id, hall_id, performance_date, performance_time, 
                status, created_at, updated_at
            )
            SELECT DISTINCT
                ps.series_id,
                v.venue_id,
                h.hall_id,
                l.PERFORMANCE_DATE,
                l.PERFORMANCE_TIME,
                'SCHEDULED',
                NOW(),
                NOW()
            FROM PERFORMANCES.LOCATION l
            JOIN PERFORMANCES.PERFORMANCE p ON l.PERFORMANCE = p.ID
            JOIN event_management.performance_series ps ON ps.series_name = p.NAME AND ps.client_id = p.CUSTOMER
            JOIN event_management.venues v ON v.venue_name = l.NAME
            JOIN event_management.halls h ON h.venue_id = v.venue_id
            WHERE l.PERFORMANCE_DATE IS NOT NULL
            ON DUPLICATE KEY UPDATE
                performance_date = VALUES(performance_date),
                performance_time = VALUES(performance_time),
                status = VALUES(status),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} individual performances", rowsAffected);
    }

    /**
     * Update client addresses for any missing data
     */
    private void updateClientAddresses() {
        log.info("Updating client addresses...");
        
        String sql = """
            UPDATE event_management.clients c
            JOIN PERFORMANCES.CUSTOMER src ON c.client_id = src.ID
            LEFT JOIN PERFORMANCES.ADDRESS a ON src.ADDRESS = a.ID
            SET c.address = CONCAT_WS(', ', 
                NULLIF(a.STREET, ''), 
                NULLIF(a.HOUSENUMBER, ''),
                NULLIF(a.CITY, ''),
                NULLIF(a.ZIPCODE, '')
            ),
            c.updated_at = NOW()
            WHERE (c.address IS NULL OR c.address = '') AND a.ID IS NOT NULL
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Updated {} client addresses", rowsAffected);
    }

    /**
     * Migrate files
     */
    private void migrateFiles() {
        log.info("Migrating files...");
        
        String sql = """
            INSERT INTO event_management.files (
                client_id, file_name, file_path, file_type, file_size, created_at, updated_at
            )
            SELECT DISTINCT
                f.CUSTOMER,
                f.FILENAME,
                f.PATH,
                f.TYPE,
                f.SIZE,
                NOW(),
                NOW()
            FROM PERFORMANCES.FILE f
            WHERE f.CUSTOMER IS NOT NULL AND f.FILENAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                file_name = VALUES(file_name),
                file_path = VALUES(file_path),
                file_type = VALUES(file_type),
                file_size = VALUES(file_size),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} files", rowsAffected);
    }

    /**
     * Get migration statistics
     */
    public Map<String, Object> getMigrationStatistics() {
        log.info("Gathering migration statistics...");
        
        Map<String, Object> stats = Map.of(
            "clients", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.clients", Integer.class),
            "venues", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.venues", Integer.class),
            "halls", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.halls", Integer.class),
            "artists", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.artists", Integer.class),
            "performance_series", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.performance_series", Integer.class),
            "performances", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.performances", Integer.class),
            "files", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.files", Integer.class)
        );
        
        log.info("Migration statistics: {}", stats);
        return stats;
    }
}
