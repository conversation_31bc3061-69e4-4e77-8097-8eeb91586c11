package org.acum.acumjobs.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class UnifiedMigrationService {

    private final JdbcTemplate sourceJdbcTemplate;
    private final JdbcTemplate targetJdbcTemplate;
    private final MigrationLoggingService migrationLoggingService;

    /**
     * Execute a complete migration workflow in the optimal order
     */
    @Transactional("targetTransactionManager")
    public void executeCompleteMigration() {
        log.info("Starting complete unified migration process...");
        
        try {
            // Step 1: Core entities (independent)
            migrateClients();
            migrateVenuesAndHalls();
            migrateArtists();
            
            // Step 2: Dependent entities
            migratePerformanceSeries();
            migratePerformances();
            
            // Step 3: Additional data
            updateClientAddresses();
            migrateFiles();

            // Step 4: Works and related data
            migratePerformanceSeriesWorks();
            migrateDirectLicenses();
            migrateMessages();
            
            log.info("Complete unified migration completed successfully");
            
        } catch (Exception e) {
            log.error("Complete unified migration failed", e);
            throw new RuntimeException("Complete migration failed", e);
        }
    }

    /**
     * Migrate clients with address information embedded
     */
    private void migrateClients() {
        log.info("Migrating clients with addresses...");
        
        String sql = """
            INSERT INTO event_management.clients (
                client_id, client_name, client_type, client_sub_type, business_number,
                client_phone, secondary_phone, email, website, address, client_status, created_at, updated_at
            )
            SELECT DISTINCT
                c.ID as client_id,
                c.NAME as client_name,
                CASE
                    WHEN c.TYPES LIKE '%INDIVIDUAL%' THEN 'INDIVIDUAL'
                    WHEN c.TYPES LIKE '%ORGANIZATION%' THEN 'ORGANIZATION'
                    ELSE 'ORGANIZATION'
                END as client_type,
                CASE
                    WHEN c.SUB_TYPE IS NOT NULL THEN c.SUB_TYPE
                    ELSE 'STANDARD'
                END as client_sub_type,
                COALESCE(c.BUS_NUMBER, c.AUTH_NUMBER, CAST(c.ID AS CHAR)) as business_number,
                COALESCE(cd.PHONE, '') as client_phone,
                COALESCE(cd.MOBILE, '') as secondary_phone,
                COALESCE(cd.EMAIL, '') as email,
                COALESCE(cd.WEBSITE, '') as website,
                CONCAT_WS(', ',
                    NULLIF(a.STREET, ''),
                    NULLIF(a.HOUSE_NUMBER, ''),
                    NULLIF(a.CITY, ''),
                    NULLIF(a.ZIP_CODE, '')
                ) as address,
                'ACTIVE' as client_status,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.CUSTOMER c
            LEFT JOIN PERFORMANCES.ADDRESS a ON a.CUSTOMER = c.ID
            LEFT JOIN PERFORMANCES.CONTACT_DETAILS cd ON cd.CUSTOMER = c.ID
            WHERE c.ID IS NOT NULL AND c.NAME IS NOT NULL
            GROUP BY c.ID
            ON DUPLICATE KEY UPDATE
                client_name = VALUES(client_name),
                client_type = VALUES(client_type),
                client_sub_type = VALUES(client_sub_type),
                business_number = VALUES(business_number),
                client_phone = VALUES(client_phone),
                secondary_phone = VALUES(secondary_phone),
                email = VALUES(email),
                website = VALUES(website),
                address = VALUES(address),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} clients with addresses", rowsAffected);
    }

    /**
     * Migrate venues and halls in one operation
     */
    private void migrateVenuesAndHalls() {
        log.info("Migrating venues and halls...");
        
        // First migrate venues
        String venuesSql = """
            INSERT INTO event_management.venues (
                venue_name, venue_address, venue_city, created_at, updated_at
            )
            SELECT DISTINCT
                l.NAME as venue_name,
                COALESCE(l.ADDRESS, '') as venue_address,
                COALESCE(l.CITY, '') as venue_city,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.LOCATION l
            WHERE l.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                venue_name = VALUES(venue_name),
                venue_address = VALUES(venue_address),
                venue_city = VALUES(venue_city),
                updated_at = NOW()
            """;
        
        int venuesAffected = targetJdbcTemplate.update(venuesSql);
        log.info("Migrated {} venues", venuesAffected);
        
        // Then migrate halls
        String hallsSql = """
            INSERT INTO event_management.halls (
                venue_id, hall_name, capacity, created_at, updated_at
            )
            SELECT 
                v.venue_id,
                COALESCE(l.HALL_NAME, 'Main Hall'),
                l.CAPACITY,
                NOW(),
                NOW()
            FROM PERFORMANCES.LOCATION l
            JOIN event_management.venues v ON v.venue_name = l.NAME
            WHERE l.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                hall_name = VALUES(hall_name),
                capacity = VALUES(capacity),
                updated_at = NOW()
            """;
        
        int hallsAffected = targetJdbcTemplate.update(hallsSql);
        log.info("Migrated {} halls", hallsAffected);
    }

    /**
     * Migrate artists from performers
     */
    private void migrateArtists() {
        log.info("Migrating artists from performers...");
        
        String sql = """
            INSERT INTO event_management.artists (
                artist_name, artist_region, artist_country, created_at, updated_at
            )
            SELECT DISTINCT
                p.NAME as artist_name,
                CASE
                    WHEN p.TYPE = 1 THEN 'LOCAL'
                    WHEN p.TYPE = 2 THEN 'FOREIGN'
                    ELSE 'LOCAL'
                END as artist_region,
                CASE
                    WHEN p.TYPE = 2 THEN COALESCE(p.COUNTRY, 'Unknown')
                    ELSE 'Israel'
                END as artist_country,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.PERFORMER p
            WHERE p.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                artist_name = VALUES(artist_name),
                artist_region = VALUES(artist_region),
                artist_country = VALUES(artist_country),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} artists", rowsAffected);
    }

    /**
     * Migrate performance series with artist mapping
     */
    private void migratePerformanceSeries() {
        log.info("Migrating performance series...");
        
        String sql = """
            INSERT INTO event_management.performance_series (
                series_name, performance_duration, client_id, arranger, artist_id,
                performance_type_id, series_status, works_status, is_works_arranged,
                has_warmup_show, created_at, updated_at
            )
            SELECT DISTINCT
                p.NAME as series_name,
                TIME('01:30:00') as performance_duration,
                p.CUSTOMER as client_id,
                COALESCE(p.ARRANGER, '') as arranger,
                a.id as artist_id,
                1 as performance_type_id,
                'ACTIVE' as series_status,
                'EMPTY' as works_status,
                false as is_works_arranged,
                false as has_warmup_show,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.PERFORMANCE p
            LEFT JOIN PERFORMANCES.PERFORMER perf ON p.PERFORMER = perf.ID
            LEFT JOIN event_management.artists a ON a.artist_name = perf.NAME
            WHERE p.CUSTOMER IS NOT NULL AND p.NAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                series_name = VALUES(series_name),
                performance_duration = VALUES(performance_duration),
                arranger = VALUES(arranger),
                artist_id = VALUES(artist_id),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} performance series", rowsAffected);
    }

    /**
     * Migrate individual performances
     */
    private void migratePerformances() {
        log.info("Migrating individual performances...");
        
        String sql = """
            INSERT INTO event_management.performances (
                series_id, client_id, venue_id, hall_id, performance_date, performance_time,
                ticket_sales, audience_size, reporting_source_type, paying_entity_transfer_id,
                status, signed, created_at, updated_at
            )
            SELECT DISTINCT
                ps.id as series_id,
                ps.client_id as client_id,
                v.id as venue_id,
                h.id as hall_id,
                COALESCE(l.PERFORMANCE_DATE, CURDATE()) as performance_date,
                COALESCE(l.PERFORMANCE_TIME, TIME('20:00:00')) as performance_time,
                false as ticket_sales,
                COALESCE(l.AUDIENCE_SIZE, 0) as audience_size,
                'MANUAL' as reporting_source_type,
                1 as paying_entity_transfer_id,
                'PENDING_TREATMENT' as status,
                false as signed,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.LOCATION l
            JOIN PERFORMANCES.PERFORMANCE p ON l.PERFORMANCE = p.ID
            JOIN event_management.performance_series ps ON ps.series_name = p.NAME AND ps.client_id = p.CUSTOMER
            JOIN event_management.venues v ON v.venue_name = l.NAME
            JOIN event_management.halls h ON h.venue_id = v.id
            WHERE l.PERFORMANCE_DATE IS NOT NULL
            ON DUPLICATE KEY UPDATE
                performance_date = VALUES(performance_date),
                performance_time = VALUES(performance_time),
                audience_size = VALUES(audience_size),
                status = VALUES(status),
                updated_at = NOW()
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} individual performances", rowsAffected);
    }

    /**
     * Update client addresses for any missing data
     */
    private void updateClientAddresses() {
        log.info("Updating client addresses...");
        
        String sql = """
            UPDATE event_management.clients c
            JOIN PERFORMANCES.CUSTOMER src ON c.client_id = src.ID
            LEFT JOIN PERFORMANCES.ADDRESS a ON src.ADDRESS = a.ID
            SET c.address = CONCAT_WS(', ', 
                NULLIF(a.STREET, ''), 
                NULLIF(a.HOUSENUMBER, ''),
                NULLIF(a.CITY, ''),
                NULLIF(a.ZIPCODE, '')
            ),
            c.updated_at = NOW()
            WHERE (c.address IS NULL OR c.address = '') AND a.ID IS NOT NULL
            """;
        
        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Updated {} client addresses", rowsAffected);
    }

    /**
     * Migrate files with proper relationship mapping
     */
    private void migrateFiles() {
        log.info("Migrating files...");

        String sql = """
            INSERT INTO event_management.file_uploads (
                file_name, file_path, file_type, uploaded_by, uploaded_at,
                related_table, related_record_id, status
            )
            SELECT DISTINCT
                f.ORIGINALNAME,
                f.SYSTEMNAME,
                f.TYPE,
                f.USERID,
                f.TIMESTAMP,
                CASE
                    WHEN f.PERFORMANCE IS NOT NULL THEN 'performance_series'
                    ELSE 'unknown'
                END as related_table,
                CASE
                    WHEN f.PERFORMANCE IS NOT NULL THEN ps.series_id
                    ELSE NULL
                END as related_record_id,
                COALESCE(f.STATUS, 'ACTIVE') as status
            FROM PERFORMANCES.FILE f
            LEFT JOIN PERFORMANCES.PERFORMANCE p ON f.PERFORMANCE = p.ID
            LEFT JOIN event_management.performance_series ps ON ps.series_name = p.NAME AND ps.client_id = p.CUSTOMER
            WHERE f.ORIGINALNAME IS NOT NULL
            ON DUPLICATE KEY UPDATE
                file_name = VALUES(file_name),
                file_path = VALUES(file_path),
                file_type = VALUES(file_type),
                uploaded_by = VALUES(uploaded_by),
                uploaded_at = VALUES(uploaded_at),
                related_table = VALUES(related_table),
                related_record_id = VALUES(related_record_id),
                status = VALUES(status)
            """;

        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} files", rowsAffected);
    }

    /**
     * Migrate works from WORK table to performance_series_works
     */
    @Transactional("targetTransactionManager")
    public void migratePerformanceSeriesWorks() {
        log.info("Migrating performance series works...");

        String sql = """
            INSERT INTO event_management.performance_series_works (
                series_id, client_id, creator_id, work_name, work_performer,
                work_arranger, work_duration, acum_work_id, version_id, is_handled, created_at, updated_at
            )
            SELECT DISTINCT
                ps.id as series_id,
                ps.client_id as client_id,
                1 as creator_id,
                w.NAME as work_name,
                COALESCE(w.PERFORMER, 'Unknown') as work_performer,
                COALESCE(w.ARRANGER, '') as work_arranger,
                CASE
                    WHEN w.DURATION IS NOT NULL AND w.DURATION != '' THEN
                        STR_TO_DATE(CONCAT('00:', SUBSTRING_INDEX(w.DURATION, ':', -2)), '%H:%i:%s')
                    WHEN w.NEWDURATION IS NOT NULL AND w.NEWDURATION != '' THEN
                        STR_TO_DATE(CONCAT('00:', SUBSTRING_INDEX(w.NEWDURATION, ':', -2)), '%H:%i:%s')
                    ELSE TIME('00:03:00')
                END as work_duration,
                w.NUMBER as acum_work_id,
                CAST(COALESCE(w.VERSION, 1) AS CHAR) as version_id,
                COALESCE(w.HANDLED = 1, false) as is_handled,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.WORK w
            JOIN PERFORMANCES.PERFORMANCE p ON w.PERFORMANCE = p.ID
            JOIN event_management.performance_series ps ON ps.series_name = p.NAME AND ps.client_id = p.CUSTOMER
            WHERE (w.DELFLAG IS NULL OR w.DELFLAG = 0)
            AND w.NAME IS NOT NULL
            AND w.NAME != ''
            ON DUPLICATE KEY UPDATE
                work_name = VALUES(work_name),
                work_performer = VALUES(work_performer),
                work_arranger = VALUES(work_arranger),
                work_duration = VALUES(work_duration),
                acum_work_id = VALUES(acum_work_id),
                version_id = VALUES(version_id),
                is_handled = VALUES(is_handled),
                updated_at = NOW()
            """;

        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} performance series works", rowsAffected);
    }

    /**
     * Migrate DL_WORKS to direct_licenses table
     */
    @Transactional("targetTransactionManager")
    public void migrateDirectLicenses() {
        log.info("Migrating direct licenses...");

        String sql = """
            INSERT INTO event_management.direct_licenses (
                client_id, series_id, creator_id, file_id, from_date, to_date, status, created_at, updated_at
            )
            SELECT DISTINCT
                ps.client_id as client_id,
                ps.id as series_id,
                1 as creator_id,
                1 as file_id,
                COALESCE(DATE(p.TIMESTAMP), CURDATE()) as from_date,
                DATE_ADD(COALESCE(DATE(p.TIMESTAMP), CURDATE()), INTERVAL 1 YEAR) as to_date,
                'PENDING_TREATMENT' as status,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.DL_WORK dw
            JOIN PERFORMANCES.PERFORMANCE p ON dw.PERFORMANCE = p.ID
            JOIN event_management.performance_series ps ON ps.series_name = p.NAME AND ps.client_id = p.CUSTOMER
            WHERE (dw.DELFLAG IS NULL OR dw.DELFLAG = 0)
            AND dw.NAME IS NOT NULL
            AND dw.NAME != ''
            ON DUPLICATE KEY UPDATE
                from_date = VALUES(from_date),
                to_date = VALUES(to_date),
                status = VALUES(status),
                updated_at = NOW()
            """;

        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} direct licenses", rowsAffected);
    }

    /**
     * Migrate EMAIL records to messages table
     */
    @Transactional("targetTransactionManager")
    public void migrateMessages() {
        log.info("Migrating messages...");

        String sql = """
            INSERT INTO event_management.messages (
                client_id, user_id, message_external_id, type, message_template_id,
                category, subject, content, status, sent_at, created_at, updated_at
            )
            SELECT DISTINCT
                e.CUSTOMER as client_id,
                COALESCE(e.FROMUSER, 1) as user_id,
                CAST(e.ID AS CHAR) as message_external_id,
                'EMAIL' as type,
                1 as message_template_id,
                CASE
                    WHEN e.TYPE LIKE '%payment%' OR e.TYPE LIKE '%invoice%' THEN 'PAYMENT'
                    WHEN e.TYPE LIKE '%performance%' OR e.TYPE LIKE '%show%' THEN 'PERFORMANCE'
                    WHEN e.TYPE LIKE '%license%' THEN 'LICENSE'
                    ELSE 'GENERAL'
                END as category,
                COALESCE(e.TITLE, 'No Subject') as subject,
                COALESCE(e.CONTENT, '') as content,
                CASE
                    WHEN e.STATUS = 'SENT' THEN 'SENT'
                    WHEN e.STATUS = 'DELIVERED' THEN 'SENT'
                    WHEN e.STATUS = 'FAILED' THEN 'FAILED'
                    ELSE 'INBOX'
                END as status,
                e.TIMESTAMP as sent_at,
                NOW() as created_at,
                NOW() as updated_at
            FROM PERFORMANCES.EMAIL e
            WHERE e.CUSTOMER IS NOT NULL
            AND e.TITLE IS NOT NULL
            AND e.TITLE != ''
            ON DUPLICATE KEY UPDATE
                subject = VALUES(subject),
                content = VALUES(content),
                status = VALUES(status),
                sent_at = VALUES(sent_at),
                updated_at = NOW()
            """;

        int rowsAffected = targetJdbcTemplate.update(sql);
        log.info("Migrated {} messages", rowsAffected);
    }

    /**
     * Get migration statistics
     */
    public Map<String, Object> getMigrationStatistics() {
        log.info("Gathering migration statistics...");
        
        Map<String, Object> stats = Map.of(
            "clients", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.clients", Integer.class),
            "venues", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.venues", Integer.class),
            "halls", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.halls", Integer.class),
            "artists", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.artists", Integer.class),
            "performance_series", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.performance_series", Integer.class),
            "performances", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.performances", Integer.class),
            "file_uploads", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.file_uploads", Integer.class),
            "performance_series_works", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.performance_series_works", Integer.class),
            "direct_licenses", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.direct_licenses", Integer.class),
            "messages", targetJdbcTemplate.queryForObject("SELECT COUNT(*) FROM event_management.messages", Integer.class)
        );
        
        log.info("Migration statistics: {}", stats);
        return stats;
    }
}
