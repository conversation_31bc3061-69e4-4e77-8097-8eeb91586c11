package org.acum.acumjobs.data.repositories;

import org.acum.acumjobs.data.entities.Address;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional("sourceTransactionManager")
public interface AddressRepository extends JpaRepository<Address, Long> {
    // Simplified repository - complex queries moved to UnifiedMigrationService
}
