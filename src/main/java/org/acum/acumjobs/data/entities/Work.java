package org.acum.acumjobs.data.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "WORK", schema = "PERFORMANCES")
public class Work {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "WORK_id_gen")
    @SequenceGenerator(name = "WORK_id_gen", sequenceName = "SQL160705131826460", allocationSize = 1)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "NAME", length = 100)
    private String name;

    @Column(name = "ENGNAME", length = 100)
    private String engname;

    @Column(name = "DURATION", length = 10)
    private String duration;

    @Column(name = "LITERATURE")
    private Short literature;

    @Column(name = "OPENING")
    private Short opening;

    @Column(name = "NUMBER", length = 10)
    private String number;

    @Column(name = "VERSION")
    private Short version;

    @Column(name = "LANGUAGE")
    private Short language;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PERFORMANCE")
    private Performance performance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PLAYLIST")
    private Playlist playlist;

    @Column(name = "PERFORMER", length = 150)
    private String performer;

    @Column(name = "NEWDURATION", length = 10)
    private String newduration;

    @Column(name = "HANDLED")
    private Short handled;

    @Column(name = "ORIGIN")
    private Short origin;

    @Column(name = "DELFLAG")
    private Short delflag;

}