package org.acum.acumjobs.data.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "DL_WORK", schema = "PERFORMANCES")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DlWork {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DL_WORK_id_gen")
    @SequenceGenerator(name = "DL_WORK_id_gen", sequenceName = "SQL240407184230140", allocationSize = 1)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "NAME", length = 100)
    private String name;

    @Column(name = "NUMBER", length = 10)
    private String number;

    @Column(name = "VERSION")
    private Short version;

    @Column(name = "PERFORMANCE")
    private Long performance;

    @Column(name = "DIRECTTLICENSE")
    private Long directtlicense;

    @Column(name = "PERFORMER", length = 150)
    private String performer;

    @Column(name = "DELFLAG")
    private Short delflag;

}