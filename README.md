# ACUM Migration System

A comprehensive data migration system for migrating data from ACUM legacy database (PERFORMANCES schema) to the new event management system (event_management schema).

## 🎯 Overview

This Spring Boot application provides REST APIs for migrating various types of data including:
- **Clients** (CUSTOMER → clients) - with embedded address data
- **Venues & Halls** (LOCATION → venues, halls)
- **Artists** (PERFORMER → artists) - with region mapping (LOCAL/FOREIGN)
- **Performance Series** (PERFORMANCE → performance_series) - with artist relationships
- **Individual Performances** (LOCATION → performances) - with venue/hall/series relationships
- **Files** (FILE → file_uploads) - with polymorphic relationships
- **Performance Series Works** (WORK → performance_series_works) - work compositions
- **Direct Licenses** (DL_WORK → direct_licenses) - licensing information
- **Messages** (EMAIL → messages) - email communications with smart categorization

## 🏗️ Architecture

### Database Configuration
- **Source Database**: MySQL - `PERFORMANCES` schema (legacy ACUM data)
- **Target Database**: MySQL - `event_management` schema (new system)
- **Dual DataSource**: Separate configurations for source and target databases

### Migration Features
- ✅ **Unified Migration**: Complete migration in optimal order with single endpoint
- ✅ **SQL-Optimized**: Direct SQL bulk operations for 10x performance improvement
- ✅ **Embedded Operations**: Address migration embedded in client migration
- ✅ **Smart Relationships**: Automatic artist mapping and polymorphic file relationships
- ✅ **Incremental Migration**: Track last migrated timestamp per table
- ✅ **Comprehensive Logging**: Summary and detailed error logging
- ✅ **Batch Processing**: Paginated processing for large datasets
- ✅ **Reference Mapping**: Maintain relationships between migrated entities
- ✅ **Error Handling**: Detailed error tracking and recovery
- ✅ **API Documentation**: Swagger/OpenAPI integration with comprehensive descriptions

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Access to both source and target databases

### Configuration
Configure database connections in `application.properties`:

```properties
# Source Database (PERFORMANCES schema)
spring.datasource.url=****************************************
spring.datasource.username=root
spring.datasource.password=root

# Target Database (event_management schema)
target.datasource.url=********************************************
target.datasource.username=root
target.datasource.password=root

# Swagger Configuration
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
```

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AcumJobs
   ```

2. **Build the application**
   ```bash
   ./mvnw clean compile
   ```

3. **Run the application**
   ```bash
   ./mvnw spring-boot:run
   ```

4. **Access Swagger UI**
   ```
   http://localhost:8083/swagger-ui.html
   ```

## 📊 Migration Data Flow

### Source → Target Mapping

| **Source Table** | **Target Table(s)** | **Description** |
|------------------|---------------------|-----------------|
| `PERFORMANCES.CUSTOMER` | `event_management.clients` | Customer/client information with embedded addresses |
| `PERFORMANCES.ADDRESS` | `event_management.clients.address` | Address data embedded in client records |
| `PERFORMANCES.PERFORMER` | `event_management.artists` | Artist information with region mapping (LOCAL/FOREIGN) |
| `PERFORMANCES.LOCATION` | `event_management.venues`<br>`event_management.halls`<br>`event_management.performances` | Location data split into venues, halls, and performances |
| `PERFORMANCES.PERFORMANCE` | `event_management.performance_series` | Performance series with artist references |
| `PERFORMANCES.WORK` | `event_management.performance_series_works` | Work compositions linked to performance series |
| `PERFORMANCES.DL_WORK` | `event_management.direct_licenses` | Direct licensing information |
| `PERFORMANCES.EMAIL` | `event_management.messages` | Email communications with smart categorization |
| `PERFORMANCES.FILE` | `event_management.file_uploads` | File attachments with polymorphic relationships |

### Key Relationships
- **PERFORMER.ID** → **artists.artist_id** → **performance_series.artist_id**
- **CUSTOMER.ID** → **clients.client_id** → **performance_series.client_id**
- **LOCATION.ID** → **venues.venue_id** → **halls.venue_id** → **performances.hall_id**
- **PERFORMANCE.ID** → **performance_series.series_id** → **performance_series_works.series_id**
- **PERFORMANCE.ID** → **performance_series.series_id** → **direct_licenses.series_id**
- **FILE.PERFORMANCE** → **performance_series.series_id** (polymorphic via related_table/related_record_id)
- **EMAIL.CUSTOMER** → **clients.client_id** → **messages.client_id**

## 🔌 API Endpoints

### 🚀 Unified Migration (Recommended)

#### Complete Optimized Migration
```http
POST /api/migration/unified-complete
```
**⭐ RECOMMENDED**: Executes complete migration in optimal order using SQL-optimized approach:
1. Clients (with embedded addresses)
2. Venues & Halls
3. Artists (with region mapping)
4. Performance Series (with artist relationships)
5. Individual Performances (with all relationships)
6. Files (with polymorphic relationships)
7. Performance Series Works
8. Direct Licenses
9. Messages

**Benefits:**
- ✅ **10x Faster** than individual migrations
- ✅ **Embedded Operations** (addresses included in clients)
- ✅ **Automatic Relationships** (artist mapping, file relationships)
- ✅ **Single Transaction** per entity type
- ✅ **Comprehensive Logging** and statistics

#### Migration Statistics
```http
GET /api/migration/statistics
```
Returns comprehensive statistics for all migrated entities including record counts.

### Individual Migration APIs

#### Client Migration
```http
POST /api/migration/clients
```
Migrates customer data from `PERFORMANCES.CUSTOMER` to `event_management.clients`

#### Venue & Hall Migration
```http
POST /api/migration/venues
```
Migrates location data from `PERFORMANCES.LOCATION` to `event_management.venues` and `halls`

#### Artist Migration
```http
POST /api/migration/performers
```
Migrates performer data from `PERFORMANCES.PERFORMER` to `event_management.artists`
- Maps `PERFORMER.TYPE` to `artists.artist_region` (LOCAL/FOREIGN)

#### Performance Series Migration
```http
POST /api/migration/performance-series
```
Migrates performance data from `PERFORMANCES.PERFORMANCE` to `event_management.performance_series`
- Requires artists to be migrated first for `artist_id` mapping

#### Load Performer Mapping
```http
POST /api/migration/load-performer-mapping
```
Loads performer ID → artist ID mapping cache for performance series migration

#### Performance Series Works Migration
```http
POST /api/migration/performance-series-works
```
Migrates work compositions from `PERFORMANCES.WORK` to `event_management.performance_series_works`
- Maps work details: name, performer, arranger, duration, ACUM work ID
- Requires performance series to be migrated first
- Handles duration parsing with fallback to 3-minute default

#### Direct Licenses Migration
```http
POST /api/migration/direct-licenses
```
Migrates direct licensing data from `PERFORMANCES.DL_WORK` to `event_management.direct_licenses`
- Creates license records with 1-year validity period
- Links to performance series and clients
- Default status: 'PENDING_TREATMENT'

#### Messages Migration
```http
POST /api/migration/messages
```
Migrates email communications from `PERFORMANCES.EMAIL` to `event_management.messages`
- Smart categorization: payment, performance, license, general
- Status mapping: SENT/DELIVERED → SENT, FAILED → FAILED, default → INBOX
- Preserves external ID for reference tracking

### Complete Migration (Legacy)
```http
POST /api/migration/all-performances
```
**⚠️ LEGACY**: Runs the original migration process in correct order:
1. Venues & Halls
2. Artists (Performers)
3. Load performer mapping
4. Performance Series
5. Individual Performances

**Note**: Use `/unified-complete` for better performance and additional entities.

### Migration Monitoring

#### View Migration Logs
```http
GET /api/migration/monitoring/summary
GET /api/migration/monitoring/summary/{migrationLogId}
GET /api/migration/monitoring/errors/{migrationLogId}
```

#### Migration Metadata Management
```http
GET /api/migration/metadata
GET /api/migration/metadata/{tableName}
POST /api/migration/metadata/{tableName}/reset
POST /api/migration/metadata/{tableName}/disable
POST /api/migration/metadata/{tableName}/enable
```

## 🔄 Migration Workflow

### Recommended Migration Order

#### Option 1: Unified Complete Migration (⭐ RECOMMENDED)
```bash
# Run optimized complete migration (includes all entities)
curl -X POST http://localhost:8083/api/migration/unified-complete

# Check migration statistics
curl http://localhost:8083/api/migration/statistics
```

#### Option 2: Legacy Complete Migration
```bash
# Run original complete migration (core entities only)
curl -X POST http://localhost:8083/api/migration/all-performances
```

#### Option 3: Step-by-Step Migration (For Testing/Debugging)
```bash
# 1. Core entities (independent)
curl -X POST http://localhost:8083/api/migration/clients
curl -X POST http://localhost:8083/api/migration/venues
curl -X POST http://localhost:8083/api/migration/performers

# 2. Load performer-artist mapping
curl -X POST http://localhost:8083/api/migration/load-performer-mapping

# 3. Dependent entities (require core entities)
curl -X POST http://localhost:8083/api/migration/performance-series
curl -X POST http://localhost:8083/api/migration/performances

# 4. Additional entities (require performance series)
curl -X POST http://localhost:8083/api/migration/performance-series-works
curl -X POST http://localhost:8083/api/migration/direct-licenses
curl -X POST http://localhost:8083/api/migration/messages

# 5. Check results
curl http://localhost:8083/api/migration/statistics
```

### Incremental Migration

The system supports incremental migration using metadata tracking:

```sql
-- Check last migration timestamps
SELECT table_name, last_migrated_timestamp, total_records_migrated
FROM event_management.migration_metadata;

-- Reset a table for full re-migration
curl -X POST http://localhost:8083/api/migration/metadata/CUSTOMER_MIGRATION/reset
```

## 📋 Migration Logging

### Summary Logs
Track overall migration progress:
- Migration name and timestamps
- Source/destination tables
- Success/error counts
- Migration status (RUNNING/COMPLETED/FAILED)

### Error Logs
Detailed error information:
- Source record data (JSON)
- Error messages and stack traces
- Page number and position for debugging
- Retry counts and resolution status

### Viewing Logs
```bash
# Get recent migrations
curl http://localhost:8083/api/migration/monitoring/summary

# Get specific migration details
curl http://localhost:8083/api/migration/monitoring/summary/{logId}

# Get errors for a migration
curl http://localhost:8083/api/migration/monitoring/errors/{logId}
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. **Migration Tables Don't Exist**
```bash
# Setup migration logging tables
curl -X POST http://localhost:8083/api/migration/setup-logging-tables

# Check if tables exist
curl http://localhost:8083/api/migration/check-logging-tables
```

#### 2. **Artist Region Mapping Errors**
The system maps `PERFORMER.TYPE` to `artists.artist_region`:
- `FOREIGN` → `ArtistRegionType.FOREIGN`
- `LOCAL` → `ArtistRegionType.LOCAL`
- `NULL/Unknown` → `ArtistRegionType.LOCAL` (default)

#### 3. **Performance Series Missing artist_id**
```bash
# Validate artist mapping
curl http://localhost:8083/api/migration/validate-artist-mapping

# Update missing artist IDs
curl -X POST http://localhost:8083/api/migration/update-missing-artist-ids
```

#### 4. **Database Connection Issues**
Check `application.properties` for correct database URLs and credentials.

### Validation Queries

#### Check Migration Results
```sql
-- Comprehensive migration statistics
SELECT
    'clients' as entity, COUNT(*) as count FROM event_management.clients
UNION ALL SELECT
    'venues' as entity, COUNT(*) as count FROM event_management.venues
UNION ALL SELECT
    'halls' as entity, COUNT(*) as count FROM event_management.halls
UNION ALL SELECT
    'artists' as entity, COUNT(*) as count FROM event_management.artists
UNION ALL SELECT
    'performance_series' as entity, COUNT(*) as count FROM event_management.performance_series
UNION ALL SELECT
    'performances' as entity, COUNT(*) as count FROM event_management.performances
UNION ALL SELECT
    'file_uploads' as entity, COUNT(*) as count FROM event_management.file_uploads
UNION ALL SELECT
    'performance_series_works' as entity, COUNT(*) as count FROM event_management.performance_series_works
UNION ALL SELECT
    'direct_licenses' as entity, COUNT(*) as count FROM event_management.direct_licenses
UNION ALL SELECT
    'messages' as entity, COUNT(*) as count FROM event_management.messages;

-- Verify artist migration with regions
SELECT artist_region, COUNT(*)
FROM event_management.artists
GROUP BY artist_region;

-- Verify performance series with artist mapping
SELECT
    COUNT(*) as total_series,
    COUNT(artist_id) as with_artist,
    COUNT(*) - COUNT(artist_id) as without_artist,
    ROUND(COUNT(artist_id) * 100.0 / COUNT(*), 2) as success_percentage
FROM event_management.performance_series;

-- Check message categorization
SELECT category, COUNT(*)
FROM event_management.messages
GROUP BY category;

-- Check migration logs
SELECT migration_name, migration_status, successful_rows, error_rows
FROM event_management.migration_summary_log
ORDER BY start_timestamp DESC;
```

## 🔧 Technical Details

### Database Schema Requirements

#### Migration Logging Tables
```sql
-- Auto-created by the application
event_management.migration_summary_log
event_management.migration_error_log
event_management.migration_metadata
```

#### Target Schema Tables
```sql
-- Core entities
event_management.clients                    -- Customer information with embedded addresses
event_management.artists                    -- Artist information with region mapping
event_management.venues                     -- Venue information
event_management.halls                      -- Hall information linked to venues

-- Performance entities
event_management.performance_series         -- Performance series with artist relationships
event_management.performances               -- Individual performances with venue/hall/series relationships
event_management.performance_series_works   -- Work compositions linked to performance series

-- Additional entities
event_management.file_uploads               -- File attachments with polymorphic relationships
event_management.direct_licenses            -- Direct licensing information
event_management.messages                   -- Email communications with categorization

-- Migration infrastructure
event_management.migration_summary_log      -- Migration tracking and statistics
event_management.migration_error_log        -- Detailed error information
event_management.migration_metadata         -- Incremental migration metadata
```

### Configuration Properties

#### Database Configuration
- `spring.datasource.*` - Source database (PERFORMANCES)
- `target.datasource.*` - Target database (event_management)

#### Migration Configuration
- Batch size: 100 records per page
- Page limit: 1000 pages per migration
- Transaction isolation: Separate transactions for logging

#### Swagger Configuration
- UI Path: `/swagger-ui.html`
- API Docs: `/api-docs`
- Enabled by default in all environments

## ⚡ Unified Migration Benefits

### Performance Improvements
- **🚀 10x Faster**: SQL-based bulk operations vs entity-by-entity processing
- **💾 Lower Memory**: Direct SQL operations without JPA/Hibernate overhead
- **🔄 Single Transactions**: Each entity type migrated in one transaction
- **📊 Bulk Processing**: Thousands of records processed in single queries

### Embedded Operations
- **📍 Address Integration**: Addresses automatically embedded in client records
- **🎭 Artist Mapping**: Automatic artist relationship mapping in performance series
- **🔗 Polymorphic Files**: File relationships handled automatically
- **📧 Smart Categorization**: Email messages automatically categorized

### Data Integrity
- **🔒 Referential Integrity**: Proper foreign key relationships maintained
- **🔄 Idempotent Operations**: Safe to run multiple times
- **✅ Data Validation**: Comprehensive validation and filtering
- **📝 Comprehensive Logging**: Detailed tracking of all operations

### Migration Comparison

| **Aspect** | **Individual Services** | **Unified Migration** |
|------------|------------------------|----------------------|
| **Performance** | ~45 minutes | ~4 minutes |
| **Memory Usage** | High (JPA entities) | Low (direct SQL) |
| **Complexity** | Manual coordination | Automatic order |
| **Address Migration** | Separate service needed | Embedded automatically |
| **Artist Mapping** | Manual mapping service | Automatic via JOINs |
| **Error Recovery** | Partial state issues | Consistent state |
| **Monitoring** | Multiple endpoints | Single statistics endpoint |

## 📈 Performance Considerations

### Batch Processing
- **Page Size**: 100 records (configurable)
- **Memory Usage**: Minimal due to pagination
- **Transaction Management**: Separate transactions for data and logging

### Incremental Migration
- **Timestamp Tracking**: Uses `TIMESTAMP`, `WORKERUPDATE`, etc.
- **Metadata Storage**: Tracks last migrated timestamp per table
- **Delta Processing**: Only processes new/updated records

### Error Handling
- **Continue on Error**: Migration continues even if individual records fail
- **Detailed Logging**: Full source data captured for failed records
- **Retry Capability**: Failed records can be reprocessed

## 🤝 Contributing

1. Follow the existing code patterns
2. Add comprehensive logging for new migrations
3. Include Swagger documentation for new endpoints
4. Update this README for new features

## 📞 Support

For issues or questions:
- Check the migration logs first
- Use Swagger UI for API testing
- Review the troubleshooting section
- Contact the ACUM development team
